package main

import (
	"os"
)

type Config struct {
	// Discord Bot Configuration
	BotToken string
	GuildID  string

	// Discord User Configuration (for browser automation)
	UserToken      string
	ChannelID      string
	VoiceChannelID string

	// ChromeDriver Configuration
	ChromeDriverURL string

	// Fasel HD Configuration
	FaselBaseURL string
}

func LoadConfig() *Config {
	return &Config{
		// Discord Bot Settings
		BotToken: getEnvOrDefault("DISCORD_BOT_TOKEN", "YOUR_DISCORD_BOT_TOKEN_HERE"),
		GuildID:  getEnvOrDefault("DISCORD_GUILD_ID", "YOUR_GUILD_ID_HERE"),

		// Discord User Settings (for browser automation)
		UserToken:      getEnvOrDefault("DISCORD_USER_TOKEN", "YOUR_DISCORD_USER_TOKEN_HERE"),
		ChannelID:      getEnvOrDefault("DISCORD_CHANNEL_ID", "YOUR_CHANNEL_ID_HERE"),
		VoiceChannelID: getEnvOrDefault("DISCORD_VOICE_CHANNEL_ID", "YOUR_VOICE_CHANNEL_ID_HERE"),

		// ChromeDriver Settings
		ChromeDriverURL: getEnvOrDefault("CHROMEDRIVER_URL", "http://localhost:9515"),

		// Fasel HD Settings
		FaselBaseURL: getEnvOrDefault("FASEL_BASE_URL", "https://faselhd.cloud"),
	}
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
