# Movie Bot Go - Simplified Fasel HD Discord Bot

A simplified Go version of the movie bot that streams movies from Fasel HD to Discord voice channels.

## Features

- Search movies on Fasel HD (https://faselhd.cloud)
- Interactive Discord slash commands
- Simple movie selection with dropdown menus
- Browser automation for streaming to Discord
- Basic playback controls (pause/stop)

## Prerequisites

1. **Go 1.21+** installed on your system
2. **ChromeDriver** - Download and run on port 9515
3. **Discord Bot Token** - Create a bot on Discord Developer Portal
4. **Discord Guild/Channel IDs** - Get your server and channel IDs

## Setup Instructions

### 1. Install Dependencies

```bash
go mod tidy
```

### 2. Download ChromeDriver

- Download ChromeDriver from: https://chromedriver.chromium.org/
- Extract and run it on port 9515:
```bash
./chromedriver --port=9515
```

### 3. Configure Discord Bot

1. Go to https://discord.com/developers/applications
2. Create a new application and bot
3. Copy the bot token
4. Invite the bot to your server with appropriate permissions

### 4. Configure Environment Variables

Copy the example environment file and edit it:

```bash
cp .env.example .env
```

Edit `.env` with your actual values:

```bash
# Discord Bot Configuration
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_GUILD_ID=your_guild_id_here

# Discord User Configuration (for browser automation)
DISCORD_USER_TOKEN=your_discord_user_token_here
DISCORD_CHANNEL_ID=your_text_channel_id_here
DISCORD_VOICE_CHANNEL_ID=your_voice_channel_id_here

# ChromeDriver Configuration
CHROMEDRIVER_URL=http://localhost:9515

# Fasel HD Configuration
FASEL_BASE_URL=https://faselhd.cloud
```

Load the environment variables:
```bash
source .env
```

### 5. Run the Bot

```bash
# Make sure ChromeDriver is running first
./chromedriver --port=9515

# In another terminal, load environment and run bot
source .env
go run .
```

Or use the Makefile:
```bash
# Setup everything
make setup
make deps
make chromedriver

# Run the bot
make run-env
```

## Usage

1. Start the bot with `go run .`
2. In Discord, use the slash command: `/watch <movie_name>`
3. Select a movie from the dropdown menu
4. The bot will automatically:
   - Login to Discord in a browser
   - Join your voice channel
   - Start screen sharing
   - Navigate to the movie and start playing
5. Use the control buttons to pause or stop the stream

## Commands

- `/watch <movie_name>` - Search and watch a movie from Fasel HD
- `/stop` - Stop the current stream

## Project Structure

```
moviebot-go/
├── main.go          # Entry point and Discord session setup
├── bot.go           # Discord bot logic and command handlers
├── fasel.go         # Fasel HD web scraping functionality
├── streamer.go      # Browser automation for streaming
├── config.go        # Configuration management
├── go.mod           # Go module dependencies
├── .env.example     # Environment variables template
├── Makefile         # Build and run commands
└── README.md        # This file
```

## How It Works

1. **Search**: Bot scrapes Fasel HD search results using goquery
2. **Selection**: User selects movie via Discord dropdown menu
3. **Automation**: Selenium WebDriver controls Chrome browser to:
   - Login to Discord with token injection
   - Join voice channel and start screen sharing
   - Navigate to movie URL and start playback
4. **Controls**: Discord buttons control playback (pause/stop)

## Limitations

- Requires manual configuration of Discord tokens and IDs
- Single concurrent stream support
- Simplified error handling
- Basic UI compared to original Rust version
- Requires ChromeDriver to be running separately

## Security Notes

- Never commit your Discord tokens to version control
- Use environment variables for sensitive configuration
- Be aware that token injection method may violate Discord ToS

## Troubleshooting

1. **ChromeDriver not found**: Make sure ChromeDriver is running on port 9515
2. **Discord login fails**: Check your Discord token and make sure it's valid
3. **Voice channel not found**: Verify your voice channel ID is correct
4. **Movie not playing**: Check Fasel HD site structure - selectors may need updates

## Contributing

This is a simplified educational version. Feel free to:
- Add error handling
- Improve the UI
- Add more streaming services
- Implement better configuration management

## License

This project is for educational purposes only. Respect the terms of service of all platforms used.
