package main

import (
	"fmt"
	"log"
	"time"

	"github.com/tebeka/selenium"
	"github.com/tebeka/selenium/chrome"
)

type Streamer struct {
	driver    selenium.WebDriver
	isRunning bool
	config    *Config
}

func NewStreamer(config *Config) *Streamer {
	return &Streamer{
		isRunning: false,
		config:    config,
	}
}

func (s *Streamer) Start(movieURL string) error {
	if s.isRunning {
		return fmt.Errorf("streamer is already running")
	}

	// Set up Chrome options
	opts := []chrome.Option{
		chrome.Args(
			"--ignore-ssl-errors=yes",
			"--ignore-certificate-errors",
			"--auto-select-tab-capture-source-by-title=about:blank",
			"--disable-gpu",
			"--enable-chrome-browser-cloud-management",
			"--enable-javascript",
			"--disable-blink-features=AutomationControlled",
			"--auto-accept-camera-and-microphone-capture",
			"--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
			"--start-maximized",
		),
	}

	// Set Chrome capabilities
	caps := selenium.Capabilities{"browserName": "chrome"}
	caps.AddChrome(chrome.Capabilities{Args: opts})

	// Connect to WebDriver using configured URL
	driver, err := selenium.NewRemote(caps, s.config.ChromeDriverURL)
	if err != nil {
		return fmt.Errorf("failed to create WebDriver: %v", err)
	}

	s.driver = driver
	s.isRunning = true

	// Start the streaming process
	go s.streamMovie(movieURL)

	return nil
}

func (s *Streamer) streamMovie(movieURL string) {
	defer func() {
		s.isRunning = false
		if s.driver != nil {
			s.driver.Quit()
		}
	}()

	// Step 1: Login to Discord
	err := s.loginToDiscord()
	if err != nil {
		log.Printf("Failed to login to Discord: %v", err)
		return
	}

	// Step 2: Join voice channel and start screen share
	err = s.joinVoiceChannelAndShare()
	if err != nil {
		log.Printf("Failed to join voice channel: %v", err)
		return
	}

	// Step 3: Open movie in new tab
	err = s.openMovieTab(movieURL)
	if err != nil {
		log.Printf("Failed to open movie: %v", err)
		return
	}

	// Step 4: Start the movie
	err = s.startMovie()
	if err != nil {
		log.Printf("Failed to start movie: %v", err)
		return
	}

	log.Println("Movie streaming started successfully!")

	// Keep the stream alive
	for s.isRunning {
		time.Sleep(1 * time.Second)
	}
}

func (s *Streamer) loginToDiscord() error {
	// Navigate to Discord login
	err := s.driver.Get("https://discord.com/login")
	if err != nil {
		return err
	}

	// Execute token login script using configured token
	tokenScript := fmt.Sprintf(`
		function login(token) {
			setInterval(() => {
				document.body.appendChild(document.createElement('iframe')).contentWindow.localStorage.token = '"' + token + '"';
			}, 50);
			setTimeout(() => {
				location.reload();
			}, 2500);
		}
		login('%s');
	`, s.config.UserToken)

	_, err = s.driver.ExecuteScript(tokenScript, nil)
	if err != nil {
		return err
	}

	// Wait for login to complete
	time.Sleep(4 * time.Second)

	// Navigate to specific Discord channel using configured IDs
	channelURL := fmt.Sprintf("https://discord.com/channels/%s/%s", s.config.GuildID, s.config.ChannelID)
	err = s.driver.Get(channelURL)
	if err != nil {
		return err
	}

	time.Sleep(2 * time.Second)
	return nil
}

func (s *Streamer) joinVoiceChannelAndShare() error {
	// Find and click voice channel using configured voice channel ID
	voiceChannelSelector := fmt.Sprintf("[data-list-item-id='channels___%s']", s.config.VoiceChannelID)
	voiceChannel, err := s.driver.FindElement(selenium.ByCSSSelector, voiceChannelSelector)
	if err != nil {
		return fmt.Errorf("voice channel not found: %v", err)
	}

	err = voiceChannel.Click()
	if err != nil {
		return err
	}

	time.Sleep(2 * time.Second)

	// Open new tab for the movie
	_, err = s.driver.ExecuteScript("window.open('about:blank')", nil)
	if err != nil {
		return err
	}

	// Get all window handles
	handles, err := s.driver.WindowHandles()
	if err != nil {
		return err
	}

	// Switch to Discord tab and start screen share
	err = s.driver.SwitchWindow(handles[0])
	if err != nil {
		return err
	}

	// Click screen share button (this selector might need adjustment)
	screenShareSelector := "#app-mount > div.appAsidePanelWrapper__714a6 > div.notAppAsidePanel__9d124 > div.app__52077 > div > div.layers__1c917.layers_a23c37 > div > div > div > div > div.chat__52833 > div.content__1a4fe.content_f2f8b4 > div > div.chatContent__5dca8.chatContent_a7d72e > div.membersWrap__90226.membersWrap_cbd271.hiddenMembers_cbd271 > section > div.panels__58331.panels_a4d4d9 > div > div.container__4f20e.container_b2ca13 > button:nth-child(2)"
	screenShareBtn, err := s.driver.FindElement(selenium.ByXPath, screenShareSelector)
	if err != nil {
		log.Printf("Screen share button not found, trying alternative method: %v", err)
		// Try alternative selector or method
	} else {
		err = screenShareBtn.Click()
		if err != nil {
			log.Printf("Failed to click screen share: %v", err)
		}
	}

	time.Sleep(3 * time.Second)
	return nil
}

func (s *Streamer) openMovieTab(movieURL string) error {
	// Get window handles
	handles, err := s.driver.WindowHandles()
	if err != nil {
		return err
	}

	// Switch to the new tab (should be the last one)
	if len(handles) > 1 {
		err = s.driver.SwitchWindow(handles[len(handles)-1])
		if err != nil {
			return err
		}
	}

	// Navigate to movie URL
	err = s.driver.Get(movieURL)
	if err != nil {
		return err
	}

	time.Sleep(3 * time.Second)
	return nil
}

func (s *Streamer) startMovie() error {
	// Set implicit wait
	s.driver.SetImplicitWaitTimeout(10 * time.Second)

	// Scroll down to find video player
	_, err := s.driver.ExecuteScript("window.scrollTo(0, 1100);", nil)
	if err != nil {
		log.Printf("Failed to scroll: %v", err)
	}

	// Try to find and click play button in iframe
	// This is a simplified version - you might need to adjust selectors
	playScript := `
		// Try to find iframe and switch to it
		var iframes = document.getElementsByTagName('iframe');
		if (iframes.length > 0) {
			// Focus on first iframe
			iframes[0].focus();
		}
		
		// Try to find and click play button
		var playButtons = document.querySelectorAll('button[aria-label*="play"], .jw-icon-play, .play-button');
		if (playButtons.length > 0) {
			playButtons[0].click();
		}
	`

	_, err = s.driver.ExecuteScript(playScript, nil)
	if err != nil {
		log.Printf("Failed to start movie: %v", err)
	}

	return nil
}

func (s *Streamer) Pause() error {
	if !s.isRunning || s.driver == nil {
		return fmt.Errorf("streamer is not running")
	}

	// Execute pause script
	pauseScript := `
		var video = document.querySelector('video');
		if (video) {
			if (video.paused) {
				video.play();
			} else {
				video.pause();
			}
		}
	`

	_, err := s.driver.ExecuteScript(pauseScript, nil)
	return err
}

func (s *Streamer) Stop() error {
	if !s.isRunning {
		return nil
	}

	s.isRunning = false

	if s.driver != nil {
		return s.driver.Quit()
	}

	return nil
}
