[package]
name = "moviebot-rs"
author = "Sphinx"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0.95"
image = "0.25.5"
once_cell = "1.20.3"
poise = "0.6.1"
regex = "1.11.1"
reqwest = "0.12.12"
scraper = "0.22.0"
serde = "1.0.217"
serde_json = "1.0.138"
serde_urlencoded = "0.7.1"
serenity = "0.12.4"
thirtyfour = "0.35.0"
tokio = { version = "1", features = ["full"] }
urlencoding = "2.1.3"

[target.x86_64-pc-windows-gnu]
linker = "C:\\ProgramData\\mingw64\\mingw64\\bin\\gcc.exe"
