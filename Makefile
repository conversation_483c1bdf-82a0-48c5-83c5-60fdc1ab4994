.PHONY: build run clean deps chromedriver

# Build the application
build:
	go build -o moviebot-go .

# Run the application
run:
	go run .

# Install dependencies
deps:
	go mod tidy
	go mod download

# Clean build artifacts
clean:
	rm -f moviebot-go

# Download and setup ChromeDriver (Linux/Mac)
chromedriver:
	@echo "Downloading ChromeDriver..."
	@curl -O https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_linux64.zip
	@unzip chromedriver_linux64.zip
	@chmod +x chromedriver
	@rm chromedriver_linux64.zip
	@echo "ChromeDriver downloaded. Run with: ./chromedriver --port=9515"

# Setup environment file
setup:
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "Created .env file. Please edit it with your configuration."; \
	else \
		echo ".env file already exists."; \
	fi

# Run with environment variables loaded (requires 'source .env' first)
run-env:
	@echo "Make sure to run 'source .env' first to load environment variables"
	go run .

# Help
help:
	@echo "Available commands:"
	@echo "  build       - Build the application"
	@echo "  run         - Run the application"
	@echo "  deps        - Install Go dependencies"
	@echo "  clean       - Clean build artifacts"
	@echo "  chromedriver- Download ChromeDriver"
	@echo "  setup       - Create .env file from template"
	@echo "  run-env     - Run with environment variables"
	@echo "  help        - Show this help message"
