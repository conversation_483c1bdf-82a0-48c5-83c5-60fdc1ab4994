package main

import (
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strings"

	"github.com/PuerkitoBio/goquery"
)

type Movie struct {
	Title    string
	URL      string
	ImageURL string
}

type FaselSearcher struct {
	baseURL string
	client  *http.Client
	config  *Config
}

func NewFaselSearcher(config *Config) *FaselSearcher {
	return &FaselSearcher{
		baseURL: config.FaselBaseURL,
		client:  &http.Client{},
		config:  config,
	}
}

func (f *FaselSearcher) Search(movieName string) ([]Movie, error) {
	// Encode the movie name for URL
	encodedName := url.QueryEscape(movieName)
	searchURL := fmt.Sprintf("%s/?s=%s", f.baseURL, encodedName)

	// Make HTTP request
	resp, err := f.client.Get(searchURL)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status: %d", resp.StatusCode)
	}

	// Parse HTML
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to parse HTML: %v", err)
	}

	var movies []Movie
	arabicRegex := regexp.MustCompile(`[\u0600-\u06FF]+`)

	// Find movie elements - updated selectors for faselhd.cloud
	doc.Find("div.postDiv").Each(func(i int, s *goquery.Selection) {
		if i >= 9 { // Limit to 9 results like the original
			return
		}

		// Get the link
		linkElement := s.Find("a").First()
		movieURL, exists := linkElement.Attr("href")
		if !exists {
			return
		}

		// Get the title
		titleElement := s.Find("div.h1")
		title := strings.TrimSpace(titleElement.Text())
		if title == "" {
			title = "Unknown"
		}

		// Clean Arabic characters from title
		cleanedTitle := arabicRegex.ReplaceAllString(title, "")
		cleanedTitle = strings.TrimSpace(cleanedTitle)
		if cleanedTitle == "" {
			cleanedTitle = title // Keep original if cleaning results in empty string
		}

		// Get the image URL
		imgElement := s.Find("div.imgdiv-class img")
		imageURL, exists := imgElement.Attr("data-src")
		if !exists {
			// Try regular src attribute as fallback
			imageURL, _ = imgElement.Attr("src")
		}

		movie := Movie{
			Title:    cleanedTitle,
			URL:      movieURL,
			ImageURL: imageURL,
		}

		movies = append(movies, movie)
	})

	if len(movies) == 0 {
		return nil, fmt.Errorf("no movies found")
	}

	return movies, nil
}

// GetMovieStreamURL extracts the actual streaming URL from a Fasel movie page
func (f *FaselSearcher) GetMovieStreamURL(movieURL string) (string, error) {
	resp, err := f.client.Get(movieURL)
	if err != nil {
		return "", fmt.Errorf("failed to fetch movie page: %v", err)
	}
	defer resp.Body.Close()

	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to parse movie page: %v", err)
	}

	// Look for iframe or video elements that contain the stream
	var streamURL string

	// Check for iframe with video source
	doc.Find("iframe").Each(func(i int, s *goquery.Selection) {
		src, exists := s.Attr("src")
		if exists && strings.Contains(src, "player") {
			streamURL = src
		}
	})

	// If no iframe found, look for direct video sources
	if streamURL == "" {
		doc.Find("video source").Each(func(i int, s *goquery.Selection) {
			src, exists := s.Attr("src")
			if exists {
				streamURL = src
			}
		})
	}

	if streamURL == "" {
		return movieURL, nil // Return original URL as fallback
	}

	return streamURL, nil
}
