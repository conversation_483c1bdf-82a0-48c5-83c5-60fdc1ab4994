package main

import (
	"fmt"
	"log"

	"github.com/bwmarrin/discordgo"
)

type Bot struct {
	faselSearcher *FaselSearcher
	streamer      *Streamer
	config        *Config
}

func NewBot(config *Config) (*Bot, error) {
	faselSearcher := NewFaselSearcher(config)
	streamer := NewStreamer(config)

	return &Bot{
		faselSearcher: faselSearcher,
		streamer:      streamer,
		config:        config,
	}, nil
}

func (b *<PERSON>t) handleReady(s *discordgo.Session, event *discordgo.Ready) {
	log.Printf("Bot is ready! Logged in as: %v#%v", event.User.Username, event.User.Discriminator)
}

func (b *Bot) handleInteractionCreate(s *discordgo.Session, i *discordgo.InteractionCreate) {
	switch i.ApplicationCommandData().Name {
	case "watch":
		b.handleWatchCommand(s, i)
	case "stop":
		b.handleStopCommand(s, i)
	}

	// Handle component interactions (dropdowns, buttons)
	if i.Type == discordgo.InteractionMessageComponent {
		b.handleComponentInteraction(s, i)
	}
}

func (b *Bot) handleWatchCommand(s *discordgo.Session, i *discordgo.InteractionCreate) {
	// Defer the response to give us more time
	err := s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseDeferredChannelMessageWithSource,
	})
	if err != nil {
		log.Printf("Error deferring response: %v", err)
		return
	}

	// Get the movie name from command options
	options := i.ApplicationCommandData().Options
	movieName := options[0].StringValue()

	// Search for movies
	movies, err := b.faselSearcher.Search(movieName)
	if err != nil {
		b.sendErrorResponse(s, i, "Error searching for movies: "+err.Error())
		return
	}

	if len(movies) == 0 {
		b.sendErrorResponse(s, i, "No movies found for: "+movieName)
		return
	}

	// Create dropdown with movie options
	b.sendMovieSelection(s, i, movies, movieName)
}

func (b *Bot) handleStopCommand(s *discordgo.Session, i *discordgo.InteractionCreate) {
	err := b.streamer.Stop()
	if err != nil {
		b.sendErrorResponse(s, i, "Error stopping stream: "+err.Error())
		return
	}

	err = s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
		Type: discordgo.InteractionResponseChannelMessageWithSource,
		Data: &discordgo.InteractionResponseData{
			Content: "Stream stopped successfully!",
		},
	})
	if err != nil {
		log.Printf("Error responding to stop command: %v", err)
	}
}

func (b *Bot) handleComponentInteraction(s *discordgo.Session, i *discordgo.InteractionCreate) {
	customID := i.MessageComponentData().CustomID

	if customID == "movie_select" {
		// Defer the response
		err := s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
			Type: discordgo.InteractionResponseDeferredUpdateMessage,
		})
		if err != nil {
			log.Printf("Error deferring component response: %v", err)
			return
		}

		// Get selected movie URL
		selectedValue := i.MessageComponentData().Values[0]

		// Start streaming
		err = b.streamer.Start(selectedValue)
		if err != nil {
			b.updateWithError(s, i, "Error starting stream: "+err.Error())
			return
		}

		// Update message with controls
		b.sendStreamControls(s, i)
	} else if customID == "pause_btn" {
		err := b.streamer.Pause()
		if err != nil {
			log.Printf("Error pausing stream: %v", err)
		}

		err = s.InteractionRespond(i.Interaction, &discordgo.InteractionResponse{
			Type: discordgo.InteractionResponseDeferredUpdateMessage,
		})
		if err != nil {
			log.Printf("Error responding to pause: %v", err)
		}
	} else if customID == "stop_btn" {
		err := b.streamer.Stop()
		if err != nil {
			log.Printf("Error stopping stream: %v", err)
		}

		_, err = s.InteractionResponseEdit(i.Interaction, &discordgo.WebhookEdit{
			Content:    stringPtr("Stream stopped!"),
			Embeds:     &[]*discordgo.MessageEmbed{},
			Components: &[]discordgo.MessageComponent{},
		})
		if err != nil {
			log.Printf("Error updating message: %v", err)
		}
	}
}

func (b *Bot) registerCommands(s *discordgo.Session) error {
	commands := []*discordgo.ApplicationCommand{
		{
			Name:        "watch",
			Description: "Search and watch a movie from Fasel HD",
			Options: []*discordgo.ApplicationCommandOption{
				{
					Type:        discordgo.ApplicationCommandOptionString,
					Name:        "movie",
					Description: "Name of the movie to search for",
					Required:    true,
				},
			},
		},
		{
			Name:        "stop",
			Description: "Stop the current stream",
		},
	}

	for _, cmd := range commands {
		_, err := s.ApplicationCommandCreate(s.State.User.ID, b.config.GuildID, cmd)
		if err != nil {
			return fmt.Errorf("cannot create command %s: %v", cmd.Name, err)
		}
	}

	return nil
}

func (b *Bot) sendMovieSelection(s *discordgo.Session, i *discordgo.InteractionCreate, movies []Movie, searchTerm string) {
	// Create dropdown options
	options := make([]discordgo.SelectMenuOption, 0, len(movies))
	for idx, movie := range movies {
		if idx >= 25 { // Discord limit
			break
		}
		options = append(options, discordgo.SelectMenuOption{
			Label:       movie.Title,
			Value:       movie.URL,
			Description: fmt.Sprintf("Watch %s", movie.Title),
		})
	}

	embed := &discordgo.MessageEmbed{
		Title:       fmt.Sprintf("Movies found for '%s'", searchTerm),
		Description: "Select a movie to watch:",
		Color:       0x00ff00,
	}

	components := []discordgo.MessageComponent{
		discordgo.ActionsRow{
			Components: []discordgo.MessageComponent{
				discordgo.SelectMenu{
					CustomID:    "movie_select",
					Placeholder: "Choose a movie...",
					Options:     options,
				},
			},
		},
	}

	_, err := s.InteractionResponseEdit(i.Interaction, &discordgo.WebhookEdit{
		Embeds:     &[]*discordgo.MessageEmbed{embed},
		Components: &components,
	})
	if err != nil {
		log.Printf("Error sending movie selection: %v", err)
	}
}

func (b *Bot) sendStreamControls(s *discordgo.Session, i *discordgo.InteractionCreate) {
	embed := &discordgo.MessageEmbed{
		Title:       "Stream Controls",
		Description: "Movie is now streaming! Use the controls below:",
		Color:       0x0099ff,
	}

	components := []discordgo.MessageComponent{
		discordgo.ActionsRow{
			Components: []discordgo.MessageComponent{
				discordgo.Button{
					Label:    "Pause",
					Style:    discordgo.SecondaryButton,
					CustomID: "pause_btn",
					Emoji:    discordgo.ComponentEmoji{Name: "⏸️"},
				},
				discordgo.Button{
					Label:    "Stop",
					Style:    discordgo.DangerButton,
					CustomID: "stop_btn",
					Emoji:    discordgo.ComponentEmoji{Name: "⏹️"},
				},
			},
		},
	}

	_, err := s.InteractionResponseEdit(i.Interaction, &discordgo.WebhookEdit{
		Embeds:     &[]*discordgo.MessageEmbed{embed},
		Components: &components,
	})
	if err != nil {
		log.Printf("Error sending stream controls: %v", err)
	}
}

func (b *Bot) sendErrorResponse(s *discordgo.Session, i *discordgo.InteractionCreate, message string) {
	_, err := s.InteractionResponseEdit(i.Interaction, &discordgo.WebhookEdit{
		Content: &message,
	})
	if err != nil {
		log.Printf("Error sending error response: %v", err)
	}
}

func (b *Bot) updateWithError(s *discordgo.Session, i *discordgo.InteractionCreate, message string) {
	_, err := s.InteractionResponseEdit(i.Interaction, &discordgo.WebhookEdit{
		Content:    &message,
		Embeds:     &[]*discordgo.MessageEmbed{},
		Components: &[]discordgo.MessageComponent{},
	})
	if err != nil {
		log.Printf("Error updating with error: %v", err)
	}
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}
