package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/bwmarrin/discordgo"
)

var (
	bot    *Bot
	config *Config
)

func main() {
	// Load configuration
	config = LoadConfig()

	// Initialize the bot
	var err error
	bot, err = NewBot(config)
	if err != nil {
		log.Fatal("Error creating bot:", err)
	}

	// Create Discord session
	dg, err := discordgo.New("Bot " + config.BotToken)
	if err != nil {
		log.Fatal("Error creating Discord session:", err)
	}

	// Add handlers
	dg.AddHandler(bot.handleInteractionCreate)
	dg.AddHandler(bot.handleReady)

	// Set intents
	dg.Identify.Intents = discordgo.IntentsGuildMessages | discordgo.IntentsMessageContent

	// Open connection
	err = dg.Open()
	if err != nil {
		log.Fatal("Error opening connection:", err)
	}
	defer dg.Close()

	// Register slash commands
	err = bot.registerCommands(dg)
	if err != nil {
		log.Fatal("Error registering commands:", err)
	}

	fmt.Println("Bot is now running. Press CTRL+C to exit.")

	// Wait for interrupt signal
	stop := make(chan os.Signal, 1)
	signal.Notify(stop, os.Interrupt, syscall.SIGTERM)
	<-stop

	fmt.Println("Gracefully shutting down...")
}
